package worker_payslip_detail

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"gorm.io/gorm"
)

type (
	WorkerPayslipDetailDomainItf interface {
		BulkCreateWithTx(ctx context.Context, tx *gorm.DB, params []CreateParam) ([]WorkerPayslipDetail, error)
	}

	WorkerPayslipDetailResourceItf interface {
		bulkCreateWithTx(ctx context.Context, tx *gorm.DB, params []CreateParam) ([]WorkerPayslipDetail, error)
	}
)

// BulkCreateWithTx inserts multiple worker payslip detail records in a single transaction.
func (d *WorkerPayslipDetailDomain) BulkCreateWithTx(ctx context.Context, tx *gorm.DB, params []CreateParam) ([]WorkerPayslipDetail, error) {
	details, err := d.resource.bulkCreateWithTx(ctx, tx, params)
	if err != nil {
		return []WorkerPayslipDetail{}, log.LogError(err, nil)
	}
	return details, nil
}
