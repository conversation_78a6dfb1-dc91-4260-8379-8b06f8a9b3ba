package worker_payslip_detail

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"gorm.io/gorm"
)

// bulkCreateWithTx inserts multiple worker payslip detail records in a single transaction.
func (rsc WorkerPayslipDetailResource) bulkCreateWithTx(ctx context.Context, tx *gorm.DB, params []CreateParam) ([]WorkerPayslipDetail, error) {
	if len(params) == 0 {
		return []WorkerPayslipDetail{}, nil
	}

	db := tx.WithContext(ctx)
	var details []WorkerPayslipDetail

	// Convert params to WorkerPayslipDetail structs
	for _, param := range params {
		detail := WorkerPayslipDetail{
			WorkerPayslipID:        param.WorkerPayslipID,
			WorkDate:               param.WorkDate,
			SiteName:               param.SiteName,
			StartTime:              param.StartTime,
			EndTime:                param.EndTime,
			BreakTime:              param.BreakTime,
			ExpensePerWorker:       param.ExpensePerWorker,
			TransportationExpense:  param.TransportationExpense,
			LeaderAllowance:        param.LeaderAllowance,
			DistantFeeAmount:       param.DistantFeeAmount,
			QualificationAllowance: param.QualificationAllowance,
			IncomeTaxAmount:        param.IncomeTaxAmount,
		}
		details = append(details, detail)
	}

	err := db.Create(&details).Error
	if err != nil {
		return []WorkerPayslipDetail{}, log.LogError(err, nil)
	}

	return details, nil
}
